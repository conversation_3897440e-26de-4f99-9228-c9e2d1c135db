import HaoGu<PERSON>iveAP<PERSON> from './client'
import { HaoguApi } from '../crm/client'

/**
 * Integration tests for LiveApi.
 * Provide LIVE_BASE_URL and LIVE_TOKEN to run against test/production server.
 */

const baseUrl = 'https://ddm-test.integrity.com.cn/v1/api/ke'
const appId = '7k3p9x2d5s8f1v6j'
const appSecret = 'KjRoLgkNCXxOCJBdjy3SuuK16i5gPi86mVauKWTvnJU='

describe('LiveApi integration', () => {
  jest.setTimeout(600000)
  const api = new HaoGuLiveAPI(baseUrl, appId, appSecret)

  it('getToken returns a token string', async () => {
    const payload = {
      appId,
      appSecret,
    }
    const res = await api.getToken(payload)
    console.log(res)
  })

  it('getColumns returns list', async () => {
    const res = await api.getCourses()
    console.log(res)
  })

  it('getLiveByCourseId works (if courseId provided via env)', async () => {
    const courseId = '134'
    const res = await api.getLiveByCourseId(courseId)
    console.log(res)
  })

  it('queryLiveGoodsDetails works (if channelId & productId provided)', async () => {
    const channelId = ''
    const productId = ''
    const res = await api.queryLiveGoodsDetails(channelId, Number(productId))
    console.log(res)
  })

  it('productPush works (if PRODUCT_ID provided)', async () => {
    const pid = ''
    const res = await api.productPush({ id: Number(pid) })
    console.log(res)
  })

  it('getBookByLiveId works (if LIVE_TEST_BOOK_LIVEID provided)', async () => {
    const liveId = ''
    const res = await api.getBookByLiveId({ liveId })
    console.log(res)
  })

  it('queryUserWatchList works (if LIVE_TEST_WATCH_CHANNEL provided)', async () => {
    const channel = ''

    const payload = { data: { channelId: channel, wxUnionId: 'o_-XA6s7gQJSpD9-fxU4LAJbD_vo' }, pageIndex: 1, pageSize: 10 }
    const res = await api.queryUserWatchList(payload)
    console.log(res)
  })

  it('queryLiveMessagePage works (if LIVE_TEST_MSG_CHANNEL provided)', async () => {
    const channel = ''
    const payload = { pageSize: 10, pageIndex: 1, data: { channelId: channel } }
    const res = await api.queryLiveMessagePage(payload)
    console.log(res)
  })

  it('getCourseByName', async () => {
    const res = await api.getCourseInfoByCourseName('AI营销一期（0822）')
    console.log('getCourseByName response:', res)
  }, 30000)
})
