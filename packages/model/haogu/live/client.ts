import axios, { AxiosInstance, AxiosError } from 'axios'
import type {
  ApiResponse,
  GetTokenPayload,
  ApiColumnsRes,
  ApiLiveListRes,
  ApiGoodsDetailRes,
  ApiProductPushRes,
  ApiBookListRes,
  QueryUserWatchListPayload,
  ApiQueryUserWatchListRes,
  QueryLiveMessagePagePayload,
  ApiQueryLiveMessagePageRes,
  ProductPushPayload,
} from './type'
import logger from '../../logger/logger'
import { RedisCacheDB } from '../../redis/redis_cache'

/**
 * LiveApi client generated from provided doc
 * - supports test/prod base urls
 * - add Authorization header when token is set via setToken
 */
export class HaoGuLiveAPI {
  private client: AxiosInstance
  private appId:string
  private appSecret:string

  static tokenRedisKeyPrefix:string = 'haogu:live_token'

  constructor(baseUrl: string, appId:string, appSecret:string, timeout = 10000) {
    this.client = axios.create({ baseURL: baseUrl, timeout })
    this.appId = appId
    this.appSecret = appSecret

    // 设置默认请求头
    this.client.interceptors.request.use((config) => {
      config.headers['Content-Type'] = 'application/json;charset=utf-8'
      return config
    })
  }

  /**
   * 获取并缓存token
   */
  private async _ensureToken(): Promise<string | null> {
    const redisKey = `${HaoGuLiveAPI.tokenRedisKeyPrefix}:${this.appId}`
    const redisClient = new RedisCacheDB(redisKey)

    // 先从Redis获取token
    let token = await redisClient.get()

    if (!token) {
      // 如果没有token，直接调用API获取
      try {
        const response = await this.client.post<ApiResponse<string>>('ai/live/getToken', {
          appId: this.appId,
          appSecret: this.appSecret
        })

        const { code, data } = response.data
        if (code === 0 && data) {
          token = data
          // 缓存token
          await redisClient.set(token, 60 * 60 * 24 * 20) // 20天
        } else {
          logger.error('获取token失败', response.data)
          return null
        }
      } catch (err) {
        if (err instanceof AxiosError) {
          logger.error('获取token错误', err.message, err.response?.data)
        } else {
          logger.error('获取token错误', err)
        }
        return null
      }
    }

    return token
  }

  /**
   * 统一的请求方法
   */
  private async _request<T>(method: 'GET' | 'POST', path: string, data?: any): Promise<T | null> {
    try {
      // getToken接口不需要token
      const needsToken = path !== 'ai/live/getToken'
      const headers: Record<string, string> = {}

      if (needsToken) {
        const token = await this._ensureToken()
        if (!token) {
          logger.error(`请求接口${path}失败：无法获取token`)
          return null
        }
        headers['Authorization'] = token
      }

      let response
      if (method === 'GET') {
        response = await this.client.get<ApiResponse<T>>(path, {
          params: data,
          headers
        })
      } else {
        response = await this.client.post<ApiResponse<T>>(path, data, {
          headers
        })
      }

      const resData = response.data

      if (typeof resData === 'object' && 'code' in resData && resData.code !== 0) {
        const { code, msg } = resData
        logger.error(`请求接口 ${path} 失败`, { code, msg, data })
        return null
      }

      return response.data.data
    } catch (err) {
      if (err instanceof AxiosError) {
        logger.error(`请求接口 ${path} 错误`, err.message, err.response?.data, data)
      } else {
        logger.error(`请求接口 ${path} 错误`, err)
      }
      return null
    }
  }

  // 1. 获取 token
  public async getToken(payload: GetTokenPayload): Promise<string | null> {
    return this._request<string>('POST', 'ai/live/getToken', payload)
  }

  // 2. 获取专栏列表
  public async getCourses(): Promise<ApiColumnsRes | null> {
    return this._request<ApiColumnsRes>('GET', 'ai/live/getColumns')
  }

  // 3. 获取直播列表（根据专栏）
  public async getLiveByCourseId(courseId: string): Promise<ApiLiveListRes | null> {
    return this._request<ApiLiveListRes>('GET', 'ai/live/getLiveByCourseId', { courseId })
  }

  // 4. 商品用户订单详情
  public async queryLiveGoodsDetails(channelId: string, productId: number): Promise<ApiGoodsDetailRes | null> {
    return this._request<ApiGoodsDetailRes>('GET', 'ai/live/queryLiveGoodsDetails', { channelId, productId })
  }

  // 5. 商品推送
  public async productPush(payload: ProductPushPayload): Promise<ApiProductPushRes | null> {
    return this._request<ApiProductPushRes>('POST', 'ai/live/productPush', payload)
  }

  // 6. 查询直播预约数据
  public async getBookByLiveId(payload: { liveId: string; unionId?: string }): Promise<ApiBookListRes | null> {
    return this._request<ApiBookListRes>('POST', 'ai/live/getBookByLiveId', payload)
  }

  // 7. 查询直播观看数据
  public async queryUserWatchList(payload: QueryUserWatchListPayload): Promise<ApiQueryUserWatchListRes | null> {
    if (!payload.pageIndex) {
      payload.pageIndex = 0
    }

    if (!payload.pageSize) {
      payload.pageSize = 10
    }

    return this._request<ApiQueryUserWatchListRes>('POST', 'ai/live/queryUserWatchList', payload)
  }

  // 8. 查询直播聊天数据
  public async queryLiveMessagePage(payload: QueryLiveMessagePagePayload): Promise<ApiQueryLiveMessagePageRes | null> {
    return this._request<ApiQueryLiveMessagePageRes>('POST', 'ai/live/queryLiveMessagePage', payload)
  }

  public async getCourseInfoByCourseName(courseName: string) {
    // 根据课程名获取对应课程信息
    const courseList = await this.getCourses()
    if (!courseList) {
      return null
    }

    const course = courseList.find((item) => item.courseName.includes(courseName))
    if (!course) {
      return null
    }

    const courses =  await this.getLiveByCourseId(course.courseId)

    if (!courses) {
      return null
    }

    return courses.sort((a, b) =>  new Date(a.aliveStartAt).getTime() - new Date(b.aliveStartAt).getTime())
  }
}

export default HaoGuLiveAPI
