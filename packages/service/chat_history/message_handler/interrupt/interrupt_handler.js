"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChatInterruptHandler = exports.InterruptError = void 0;
const logger_1 = require("model/logger/logger");
const redis_cache_1 = require("model/redis/redis_cache");
class InterruptError extends Error {
    constructor(message) {
        super(message);
        this.name = 'InterruptError';
    }
}
exports.InterruptError = InterruptError;
/**
 * 在有新的客户消息的时候增加版本号
 * AI 回复前进行校验
 */
class ChatInterruptHandler {
    /**
     * 构造函数是私有的，请使用静态的 `create` 方法来实例化
     * @param chat_id - 目标聊天的 ID
     * @param initialVersion - 进程开始时捕获的“预期”版本号
     */
    constructor(chat_id, initialVersion) {
        this.chat_id = chat_id;
        this.expectedVersion = initialVersion;
    }
    /**
     * 工厂方法：创建一个处理器实例，并自动获取当前的最新版本号作为预期版本。
     * 这是开始一个长流程前应该调用的方法。
     * @param chat_id - 目标聊天的 ID
     */
    static async create(chat_id) {
        const initialVersion = await this.getChatVersion(chat_id);
        return new ChatInterruptHandler(chat_id, initialVersion);
    }
    async interruptCheck() {
        const currentVersion = await ChatInterruptHandler.getChatVersion(this.chat_id);
        if (currentVersion !== this.expectedVersion) {
            const msg = '当前客户有新消息，当前流程被打断';
            logger_1.default.trace({ chat_id: this.chat_id }, msg);
            throw new InterruptError(msg);
        }
    }
    /**
     * 检查版本是否已更改，返回一个布尔值，不抛出异常。
     */
    async hasChanged() {
        const currentVersion = await ChatInterruptHandler.getChatVersion(this.chat_id);
        return this.expectedVersion !== currentVersion;
    }
    /**
     * 让指定聊天的版本号+1。
     * @param chat_id - 目标聊天的 ID
     */
    static async incrementChatVersion(chat_id) {
        return await new redis_cache_1.RedisCacheDB(this.getChatVersionKey(chat_id)).incr();
    }
    static async getChatVersion(chat_id) {
        const chatVersion = await new redis_cache_1.RedisCacheDB(this.getChatVersionKey(chat_id)).get();
        if (chatVersion !== null) {
            return parseInt(chatVersion, 10);
        }
        else {
            await new redis_cache_1.RedisCacheDB(this.getChatVersionKey(chat_id)).set(0);
            return 0;
        }
    }
    static getChatVersionKey(chat_id) {
        return `chat:${chat_id}:version`; // 使用冒号是 Redis key 命名的好习惯
    }
}
exports.ChatInterruptHandler = ChatInterruptHandler;
