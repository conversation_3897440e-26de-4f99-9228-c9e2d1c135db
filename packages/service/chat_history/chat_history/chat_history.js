"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChatHistoryService = void 0;
const messages_1 = require("@langchain/core/messages");
const chalk_1 = require("chalk");
const catchError_1 = require("lib/error/catchError");
const date_1 = require("lib/date/date");
const config_1 = require("config");
const logger_1 = require("model/logger/logger");
const interrupt_handler_1 = require("../message_handler/interrupt/interrupt_handler");
class ChatHistoryService {
    constructor(mongoClient, chatStateStoreClient) {
        this.mongoClient = mongoClient;
        this.chatStateStoreClient = chatStateStoreClient;
    }
    async getChatHistoryByChatId(chat_id, noCompress = false) {
        try {
            let chatHistory = await this.mongoClient.chat_history.findMany({
                where: {
                    chat_id: chat_id
                },
                orderBy: {
                    created_at: 'asc'
                },
                select: {
                    id: true,
                    role: true,
                    content: true,
                    created_at: true,
                    chat_id: true,
                    short_description: true,
                    is_send_by_human: true,
                    round_id: true,
                    is_recalled: true,
                    message_id: true
                }
            });
            // 如果有 archive flag，取 archive flag 往后的记录
            let latestArchiveIndex = -1;
            for (let i = chatHistory.length - 1; i >= 0; i--) {
                if (chatHistory[i].role === 'assistant' && chatHistory[i].content === ChatHistoryService.archiveFlag) {
                    latestArchiveIndex = i;
                    break;
                }
            }
            if (latestArchiveIndex !== -1) {
                chatHistory = chatHistory.slice(latestArchiveIndex + 1);
            }
            if (!noCompress) {
                chatHistory = ChatHistoryService.compressMarketingMessages(chatHistory);
            }
            return chatHistory;
        }
        catch (error) {
            console.error('Error fetching chat records:', error);
            return [];
        }
    }
    async getAllChatHistory() {
        try {
            let chatHistory = await this.mongoClient.chat_history.findMany({
                orderBy: {
                    created_at: 'asc'
                },
            });
            chatHistory = ChatHistoryService.compressMarketingMessages(chatHistory);
            return chatHistory;
        }
        catch (error) {
            console.error('Error fetching chat records:', error);
            return [];
        }
    }
    async getLLMChatHistory(chat_id, rounds) {
        let chatHistory;
        if (rounds === 0) {
            return [];
        }
        else if (rounds) {
            chatHistory = await this.getRecentConversations(chat_id, rounds, 'user');
        }
        else {
            chatHistory = await this.getChatHistoryByChatId(chat_id);
        }
        return chatHistory.map((message) => {
            if (message.role === 'user') {
                return this.createMultiModalHumanMessage(message.content, message.created_at);
            }
            else {
                return new messages_1.AIMessage(message.content);
            }
        });
    }
    /**
     * 创建支持多模态的HumanMessage
     * 检测消息中的图片URL并转换为多模态格式
     * @param content 消息内容
     * @param createdAt 消息创建时间，用于添加时间戳
     */
    createMultiModalHumanMessage(content, createdAt) {
        // 添加时间戳到消息内容中
        let contentWithTimestamp = content;
        if (createdAt) {
            const timestamp = date_1.DateHelper.formatDate(createdAt, 'YYYY-MM-DD HH:mm:ss');
            contentWithTimestamp = `[${timestamp}] ${content}`;
        }
        // 检测是否包含图片URL格式：【图片Url】https://...
        // 使用更精确的正则表达式，URL应该以空格、中文字符或字符串结尾为边界
        const imageUrlPattern = /【图片Url】(https?:\/\/[^\s\u4e00-\u9fff【】，。！？；：""''（）]*(?:\.[a-zA-Z0-9]+)*(?:\?[^\s\u4e00-\u9fff【】，。！？；：""''（）]*)?)/g;
        const imageMatches = Array.from(contentWithTimestamp.matchAll(imageUrlPattern));
        if (imageMatches.length === 0) {
            // 没有图片，返回普通文本消息
            return new messages_1.HumanMessage(contentWithTimestamp);
        }
        // 构建多模态内容
        const multiModalContent = [];
        let lastIndex = 0;
        // 处理每个图片URL
        for (const match of imageMatches) {
            const fullMatch = match[0]; // 完整匹配：【图片Url】https://...
            const imageUrl = match[1]; // 提取的URL
            const matchIndex = match.index;
            // 添加图片前的文本（如果有）
            if (matchIndex > lastIndex) {
                const textBefore = contentWithTimestamp.substring(lastIndex, matchIndex).trim();
                if (textBefore) {
                    multiModalContent.push({
                        type: 'text',
                        text: textBefore
                    });
                }
            }
            // 添加图片
            multiModalContent.push({
                type: 'image_url',
                image_url: { url: imageUrl }
            });
            lastIndex = matchIndex + fullMatch.length;
        }
        // 添加最后剩余的文本（如果有）
        if (lastIndex < contentWithTimestamp.length) {
            const textAfter = contentWithTimestamp.substring(lastIndex).trim();
            if (textAfter) {
                multiModalContent.push({
                    type: 'text',
                    text: textAfter
                });
            }
        }
        // 如果没有文本内容，添加一个默认的文本
        if (multiModalContent.every(item => item.type === 'image_url')) {
            multiModalContent.unshift({
                type: 'text',
                text: '请分析这张图片：'
            });
        }
        return new messages_1.HumanMessage({
            content: multiModalContent
        });
    }
    async clearChatHistory(chat_id, archive = true) {
        try {
            // 假删除
            if (archive) {
                await this.addBotMessage(chat_id, ChatHistoryService.archiveFlag);
            }
            else {
                const result = await this.mongoClient.chat_history.deleteMany({
                    where: {
                        chat_id
                    },
                });
                logger_1.default.debug(chat_id, `${result.count} chat records with chatId '${chat_id}' have been cleared.`);
                return result.count;
            }
        }
        catch (error) {
            console.error('Error clearing chat records:', error);
        }
    }
    async addBotMessage(chat_id, message, shortDes, options) {
        if (message) {
            await this.mongoClient.chat_history.create({
                data: {
                    chat_id: chat_id,
                    role: 'assistant',
                    content: message,
                    created_at: new Date(),
                    short_description: shortDes,
                    is_send_by_human: options?.is_send_by_human,
                    round_id: options?.round_id,
                    chat_state: await this.chatStateStoreClient.get(chat_id),
                    is_recalled: options?.is_recalled,
                    message_id: options?.message_id,
                    sop_id: options?.sop_id,
                    state: options?.state
                }
            });
        }
        if (message !== ChatHistoryService.archiveFlag) {
            logger_1.default.log({ chat_id }, chalk_1.default.greenBright(`${config_1.Config.setting.AGENT_NAME}: ${message}`));
        }
    }
    async addUserMessage(chat_id, message, roundId) {
        // 移除 打卡模版 前缀
        message = message.replace(/[\s\S]*?(比如👉：)/, '').trim();
        if (message) {
            await this.mongoClient.chat_history.create({
                data: {
                    chat_id: chat_id,
                    role: 'user',
                    content: message,
                    created_at: new Date(),
                    round_id: roundId
                }
            });
            logger_1.default.log({ chat_id }, chalk_1.default.blueBright(`客户: ${message}`));
            await interrupt_handler_1.ChatInterruptHandler.incrementChatVersion(chat_id);
        }
    }
    async getFormatChatHistoryByChatId(chat_id) {
        if (!chat_id) {
            return '';
        }
        const chatHistory = await this.getChatHistoryByChatId(chat_id);
        const formattedHistory = chatHistory.map((message) => {
            return `${message.role === 'user' ? '客户' : config_1.Config.setting.AGENT_NAME}: ${message.content.replaceAll('{', '').replaceAll('}', '')}`;
        });
        return formattedHistory.join('\n');
    }
    async formatHistoryOnRole(chat_id, role, last_rounds) {
        let chatHistory = await this.getChatHistoryByChatId(chat_id);
        chatHistory = chatHistory.filter((message) => message.role === role);
        if (last_rounds) {
            chatHistory = chatHistory.slice(-last_rounds);
        }
        const formattedHistory = chatHistory.map((message) => {
            return `${message.role === 'user' ? '客户' : config_1.Config.setting.AGENT_NAME}: ${message.content}`;
        });
        return formattedHistory.join('\n');
    }
    static formatHistoryHelper(messages) {
        const formatContent = (content) => {
            return content
                .replace(/[{}]/g, '')
                .replace(/\n+/g, '\n')
                .trim();
        };
        return messages.map((message) => {
            if (message instanceof messages_1.BaseMessage) {
                const role = message.getType() === 'human' ? '- 客户' : `  - ${config_1.Config.setting.AGENT_NAME}`;
                return `${role}：${formatContent(message.content)}`;
            }
            else {
                const role = message.role === 'user' ? '- 客户' : `  - ${config_1.Config.setting.AGENT_NAME}`;
                return `${role}：${formatContent(message.content)}`;
            }
        }).join('\n').trim();
    }
    async repeatLastMessage(chat_id, s) {
        const chatHistory = await this.getChatHistoryByChatId(chat_id);
        if (chatHistory.length === 0) {
            return false;
        }
        const lastMessage = chatHistory[chatHistory.length - 1];
        return lastMessage.content === s;
    }
    async setChatHistory(chat_id, chatHistory) {
        await this.clearChatHistory(chat_id);
        chatHistory.forEach((message) => {
            if (message.role === 'user') {
                logger_1.default.log({ chat_id }, chalk_1.default.blueBright(`客户: ${message.content}`));
            }
            else {
                logger_1.default.log({ chat_id }, chalk_1.default.greenBright(`${config_1.Config.setting.AGENT_NAME}: ${message.content}`));
            }
        });
        await this.mongoClient.chat_history.createMany({
            data: chatHistory.map((message) => {
                return {
                    chat_id: chat_id,
                    role: message.role,
                    content: message.content,
                    created_at: new Date()
                };
            })
        });
    }
    async getUserMessageCount(chat_id) {
        const chatHistory = await this.getChatHistoryByChatId(chat_id);
        return chatHistory.length;
    }
    async getMessageCount(chat_id) {
        const chatHistory = await this.getChatHistoryByChatId(chat_id);
        return chatHistory.filter((message) => message.role === 'user' || message.role === 'assistant').length;
    }
    async getLastRoundHistory(chat_id) {
        const chatHistory = await this.getChatHistoryByChatId(chat_id);
        let res = [];
        // 倒序获取一轮对话
        for (let i = chatHistory.length - 1; i >= 0; i--) {
            if (chatHistory[i].role === 'user') {
                res = chatHistory.slice(i, chatHistory.length);
                break;
            }
        }
        return res;
    }
    async getRecentConversations(chat_id, rounds, role = 'user') {
        const chatHistory = await this.getChatHistoryByChatId(chat_id);
        let res = [];
        let roundCount = 0;
        // 从后向前遍历
        for (let i = chatHistory.length - 1; i >= 0; i--) {
            // 如果是客户消息，增加轮次计数
            if (chatHistory[i].role === role) {
                roundCount++;
                // 如果达到指定轮次数，结束遍历
                if (roundCount === rounds) {
                    res = chatHistory.slice(i);
                    break;
                }
            }
            // 如果遍历到开头仍未达到指定轮次，返回全部历史
            if (i === 0) {
                res = chatHistory.slice();
            }
        }
        return res;
    }
    async getChatHistory(chat_id, rounds, maxLength = 10) {
        const chatHistory = await this.getRecentConversations(chat_id, rounds, 'user');
        let cleanChatHistory = this.filterChatHistory(chatHistory);
        if (cleanChatHistory.length > maxLength) {
            cleanChatHistory = cleanChatHistory.slice(-maxLength);
        }
        for (let i = 0; i < cleanChatHistory.length; i++) {
            if (cleanChatHistory[i].content.length > 600) {
                cleanChatHistory[i].content = cleanChatHistory[i].short_description || '[营销信息]';
            }
        }
        return ChatHistoryService.formatHistoryHelper(cleanChatHistory).replace(/\n+/g, '\n').trim();
    }
    filterChatHistory(chatHistory) {
        return chatHistory.filter((message) => {
            const nextMessage = chatHistory[chatHistory.indexOf(message) + 1];
            if (nextMessage && nextMessage.role === 'user') {
                return true;
            }
            return !message.short_description;
        });
    }
    async getLastAIMessage(chat_id) {
        const chatHistory = await this.getChatHistoryByChatId(chat_id);
        for (let i = chatHistory.length - 1; i >= 0; i--) {
            if (chatHistory[i].role === 'assistant') {
                return chatHistory[i].content;
            }
        }
        return '';
    }
    async getLastUserMessage(chat_id) {
        const chatHistory = await this.getChatHistoryByChatId(chat_id);
        for (let i = chatHistory.length - 1; i >= 0; i--) {
            if (chatHistory[i].role === 'user') {
                return chatHistory[i].content;
            }
        }
        return '';
    }
    async countRemainingMsgAfterMsg(chat_id, message) {
        const chatHistory = await this.getChatHistoryByChatId(chat_id);
        const msgIndex = chatHistory.findIndex((msg) => msg.content === message);
        if (msgIndex === -1) {
            return -1;
        }
        return chatHistory.length - msgIndex - 1;
    }
    async getLastMessage(chat_id) {
        const chatHistory = await this.getChatHistoryByChatId(chat_id);
        return chatHistory[chatHistory.length - 1];
    }
    async getUserMessages(chat_id, round) {
        if (round) {
            const chatHistory = await this.getRecentConversations(chat_id, round, 'user');
            return chatHistory.filter((message) => message.role === 'user').map((message) => message.content);
        }
        else {
            const chatHistory = await this.getChatHistoryByChatId(chat_id);
            return chatHistory.filter((message) => message.role === 'user').map((message) => message.content);
        }
    }
    async getBotMessages(chat_id) {
        const chatHistory = await this.getChatHistoryByChatId(chat_id);
        return chatHistory.filter((message) => message.role === 'assistant').map((message) => message.content);
    }
    async isRepeatedMsg(chat_id, message) {
        const chatHistory = await this.formatHistoryOnRole(chat_id, 'assistant', 10);
        // 分句检查是否重复
        // Split the sentence into clauses based on punctuation marks like period, comma, semicolon, etc.
        const clauses = message
            .split(/[,;!?，。；！？]/)
            .map((clause) => clause.trim())
            .filter((clause) => clause.length >= 5);
        const noCheckCommonClaude = 'http://';
        // 尝试从分句中查找重复的部分
        const repeatedClause = clauses.find((clause) => chatHistory.includes(clause) && !clause.includes(noCheckCommonClaude));
        let repeated = false;
        let repeatedPart = '';
        let previousSentence = '';
        if (repeatedClause) {
            repeated = true;
            repeatedPart = repeatedClause;
            // 将 chatHistory 按换行分割，并查找包含重复部分的那句话
            const sentences = chatHistory.split('\n');
            previousSentence = sentences.find((sentence) => sentence.includes(repeatedClause)) || '';
        }
        else if (chatHistory.includes(message) && message.length >= 7) {
            repeated = true;
            repeatedPart = message;
            const sentences = chatHistory.split('\n');
            previousSentence = sentences.find((sentence) => sentence.includes(message)) || '';
        }
        // 如果检测到重复，则通过 logger.log 输出当前 message、重复的历史内容及重复部分
        if (repeated) {
            console.log({ chat_id }, `重复检测 - 当前 message: ${message}`);
            console.log({ chat_id }, `重复检测 - 与之前重复的那句话: ${previousSentence}`);
            console.log({ chat_id }, `重复检测 - 重复的部分: ${repeatedPart}`);
        }
        return repeated;
    }
    async hasRepeatedMsg(chat_id, toMatch) {
        const chatHistory = await this.formatHistoryOnRole(chat_id, 'assistant');
        return chatHistory.includes(toMatch);
    }
    static compressMarketingMessages(chatHistory) {
        const totalMessages = chatHistory.length;
        return chatHistory.map((message, index) => {
            if (index >= totalMessages - 10 || !message.short_description) {
                return message;
            }
            return { ...message, content: message.short_description };
        });
    }
    /**
     * 将指定 消息放到最后
     * @param chat_id
     * @param userMessage
     */
    async moveToEnd(chat_id, userMessage) {
        if (!userMessage) {
            return;
        }
        const chatHistory = await this.getChatHistoryByChatId(chat_id);
        let message = null;
        for (let i = chatHistory.length - 1; i >= 0; i--) { // 有可能客户会重复说同一句话，倒序遍历会更稳定一些
            if (chatHistory[i].content === userMessage) {
                message = chatHistory[i];
                break;
            }
        }
        if (!message) {
            return;
        }
        await (0, catchError_1.catchError)(this.mongoClient.chat_history.update({
            where: {
                id: message.id
            },
            data: {
                created_at: new Date()
            }
        }));
    }
    /**
     @return 是否在 duration 的时间范围内有非营销的聊天记录
     * @param chatId
     * @param duration
     * @param unit
     */
    async isLastMessageWithDuration(chatId, duration, unit) {
        const chatHistory = await this.getChatHistoryByChatId(chatId);
        if (chatHistory.length < 1)
            return false;
        let lastMsg = chatHistory[chatHistory.length - 1];
        for (let i = chatHistory.length - 1; i >= 0; i--) {
            if (!chatHistory[i].short_description) {
                lastMsg = chatHistory[i];
            }
        }
        if (lastMsg.short_description)
            return false;
        const lastMsgSendTime = lastMsg.created_at;
        return date_1.DateHelper.diff(lastMsgSendTime, new Date(), unit) < duration;
    }
    async getMessageByMessageId(externalId) {
        return this.mongoClient.chat_history.findFirst({
            where: {
                message_id: externalId
            }
        });
    }
    async updateMessageId(id, messageId) {
        return this.mongoClient.chat_history.update({
            where: {
                id
            },
            data: {
                message_id: messageId
            }
        });
    }
    async setMessageRecalled(messageId) {
        const message = await this.getMessageByMessageId(messageId);
        if (!message)
            return;
        await (0, catchError_1.catchError)(this.mongoClient.chat_history.update({
            where: {
                id: message.id
            },
            data: {
                is_recalled: true
            }
        }));
    }
}
exports.ChatHistoryService = ChatHistoryService;
ChatHistoryService.archiveFlag = '[archived]';
