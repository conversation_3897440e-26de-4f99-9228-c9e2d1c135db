"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChatDB = void 0;
class ChatDB {
    constructor(mongoClient) {
        this.mongoClient = mongoClient;
    }
    async getChatByPhone(phone) {
        // @ts-ignore fuck you, primsa
        return this.mongoClient.chat.findFirst({
            where: {
                phone
            }
        });
    }
    async removeById(chat_id) {
        return this.mongoClient.chat.delete({
            where: {
                id: chat_id
            }
        });
    }
    async updateState(chat_id, chatState) {
        return this.mongoClient.chat.update({
            where: {
                id: chat_id
            },
            data: {
                chat_state: chatState
            }
        });
    }
    async create(chat) {
        // @ts-ignore fuck you, primsa
        return this.mongoClient.chat.create({
            data: chat
        });
    }
    async pushRoundId(chatId, roundId) {
        if (!await this.getById(chatId)) {
            return;
        }
        return this.mongoClient.chat.update({
            where: {
                id: chatId
            },
            data: {
                round_ids: {
                    push: roundId
                }
            }
        });
    }
    async getById(id) {
        // @ts-ignore fuck you, primsa
        return this.mongoClient.chat.findUnique({
            where: {
                id
            }
        });
    }
    async deleteById(id) {
        return this.mongoClient.chat.delete({
            where: {
                id
            }
        });
    }
    async isHumanInvolvement(chatId) {
        const chat = await this.mongoClient.chat.findUnique({
            where: {
                id: chatId
            }
        });
        if (chat) {
            return Boolean(chat.is_human_involved);
        }
        return false;
    }
    async setHumanInvolvement(chatId, isHumanInvolved) {
        return this.mongoClient.chat.update({
            where: {
                id: chatId
            },
            data: {
                is_human_involved: isHumanInvolved
            }
        });
    }
    async getCourseNo(chatId) {
        const chat = await this.mongoClient.chat.findUnique({
            where: {
                id: chatId
            }
        });
        if (chat) {
            return chat.course_no;
        }
        return null;
    }
    async getPhone(chatId) {
        const chat = await this.mongoClient.chat.findUnique({
            where: {
                id: chatId
            }
        });
        if (chat) {
            return chat.phone;
        }
        return null;
    }
    async setStopGroupPush(chatId, stopPush) {
        return this.mongoClient.chat.update({
            where: {
                id: chatId
            },
            data: {
                is_stop_group_push: stopPush
            }
        });
    }
    async getChatStateById(chat_id) {
        const chat = await this.getById(chat_id);
        if (chat) {
            return chat.chat_state;
        }
        else {
            return null;
        }
    }
    async updateContact(chatId, userId, name) {
        return this.mongoClient.chat.update({
            where: {
                id: chatId
            },
            data: {
                contact: {
                    wx_id: userId,
                    wx_name: name
                }
            }
        });
    }
}
exports.ChatDB = ChatDB;
