"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.WealthOrchardStore = exports.ChatStateStore = void 0;
const chat_state_1 = require("./chat_state");
const config_1 = require("config");
const chat_id_1 = require("config/chat_id");
/**
 * 这里的 State 是内存中的状态，每轮对话后，刷新到数据库中
 * 初始化时后，可以从数据库中读取
 */
// TODO: instance for every project
class ChatStateStore {
    constructor(chatDBClient) {
        this.state = new chat_state_1.ChatState();
        this.chatDBClient = chatDBClient;
    }
    async hasState(chat_id) {
        return this.state.get(chat_id) !== undefined;
    }
    async getFlags(chat_id) {
        return (await this.get(chat_id)).state;
    }
    static getDefaultState() {
        return {
            nextStage: 'free_talk',
            nodeInvokeCount: {},
            state: {},
            userSlots: {}
        };
    }
    async get(chat_id) {
        // 如果缓存中没有数据，尝试从数据库读取
        if (!this.state.has(chat_id)) {
            // 从数据库加载数据
            const dbState = await this.loadFromDatabase(chat_id);
            if (dbState) {
                // 数据库中有数据，使用数据库中的数据
                this.state.set(chat_id, dbState);
                console.log('从数据库加载状态成功', chat_id);
            }
            else {
                // 数据库中没有数据，创建新的状态
                this.state.set(chat_id, ChatStateStore.getDefaultState());
                // 将新创建的状态保存到数据库
                await this.saveToDatabase(chat_id);
                console.log('创建新状态并保存到数据库', chat_id);
            }
        }
        return this.state.get(chat_id);
    }
    // 从数据库加载状态
    async loadFromDatabase(chat_id) {
        try {
            return await this.chatDBClient.getChatStateById(chat_id);
        }
        catch (error) {
            console.error('从数据库加载状态失败', chat_id, error);
            return null;
        }
    }
    // 保存状态到数据库
    async saveToDatabase(chat_id) {
        try {
            // 这里实现保存到数据库的逻辑
            const state = this.state.get(chat_id);
            if (state) {
                // 如果 Chat 不存在，先新建 Chat
                if (await this.chatDBClient.getById(chat_id) === null) {
                    await this.chatDBClient.create({
                        id: chat_id,
                        round_ids: [],
                        contact: {
                            wx_id: (0, chat_id_1.getUserId)(chat_id),
                            wx_name: '', // 这里加好友后去更新
                        },
                        wx_id: config_1.Config.setting.wechatConfig?.id ?? 'local',
                        created_at: new Date(),
                        chat_state: state
                    });
                }
                else {
                    await this.chatDBClient.updateState(chat_id, state);
                }
            }
        }
        catch (error) {
            console.error('保存状态到数据库失败', chat_id, error);
        }
    }
    // 更新状态（当状态发生变化时调用）
    async update(chat_id, updates) {
        const currentState = await this.get(chat_id);
        const updatedState = ChatStateStore.deepMerge(currentState, updates);
        // 更新内存缓存
        this.state.set(chat_id, updatedState);
        // 同时更新数据库
        await this.saveToDatabase(chat_id);
    }
    clearCache(chat_id) {
        this.state.delete(chat_id);
    }
    async clear(chat_id) {
        this.state.delete(chat_id);
        // 数据库清空状态
        await this.chatDBClient.updateState(chat_id, ChatStateStore.getDefaultState());
    }
    /**
     * 注意 name 传递的是类名
     * @param chat_id
     * @param name
     */
    async getNodeCount(chat_id, name) {
        const state = await this.get(chat_id);
        if (!state.nodeInvokeCount[name]) {
            state.nodeInvokeCount[name] = 0;
        }
        return state.nodeInvokeCount[name];
    }
    async increaseNodeCount(chat_id, name) {
        const state = await this.get(chat_id);
        // 初始化节点计数为0（如果还不存在）
        if (!state.nodeInvokeCount[name]) {
            state.nodeInvokeCount[name] = 0;
        }
        // 增加调用次数
        state.nodeInvokeCount[name] += 1;
        // 保存更新后的 state
        this.state.set(chat_id, state);
    }
    static deepMerge(target, source) {
        const output = { ...target };
        if (Array.isArray(target) && Array.isArray(source)) {
            return source;
        }
        else if (typeof target === 'object' && typeof source === 'object') {
            for (const key in source) {
                if (source[key] instanceof Object && target instanceof Object && key in target) {
                    output[key] = this.deepMerge(target[key], source[key]);
                }
                else {
                    output[key] = source[key];
                }
            }
        }
        return output;
    }
}
exports.ChatStateStore = ChatStateStore;
//TODO:instantiate for every project
class WealthOrchardStore {
    static addUserMessage(chat_id, message) {
        const messages = this.userMessages.get(chat_id) || [];
        messages.push(message);
        this.userMessages.set(chat_id, messages);
    }
    static getUserMessages(chat_id) {
        return this.userMessages.get(chat_id) || [];
    }
    static clearUserMessages(chat_id) {
        this.userMessages.set(chat_id, []);
    }
}
exports.WealthOrchardStore = WealthOrchardStore;
WealthOrchardStore.userMessages = new chat_state_1.ChatState(); // 对客户的消息进行暂存
