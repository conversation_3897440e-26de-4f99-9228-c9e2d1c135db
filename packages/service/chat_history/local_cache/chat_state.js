"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChatState = void 0;
/**
 * 注意需要持久化的变量不要用这个类，需要持久化的请考虑使用 ChatStateStore 中的 State 是否可以满足需求。
 * ChatState 存储的变量只会存储到内存中，每次重启会丢失。只适合用于临时存储。
 */
class ChatState {
    constructor() {
        this._state = new Map();
    }
    has(chat_id) {
        return this._state.has(chat_id);
    }
    get(chat_id) {
        return this._state.get(chat_id);
    }
    set(chat_id, value) {
        this._state.set(chat_id, value);
    }
    delete(chat_id) {
        this._state.delete(chat_id);
    }
}
exports.ChatState = ChatState;
