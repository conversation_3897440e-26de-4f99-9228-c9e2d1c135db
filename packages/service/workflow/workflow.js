"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseWorkFlow = void 0;
exports.trackInvoke = trackInvoke;
const logger_1 = __importDefault(require("model/logger/logger"));
const chat_1 = require("../database/chat");
const prisma_1 = require("model/mongodb/prisma");
const chat_state_store_1 = require("../local_cache/chat_state_store");
const data_driven_1 = require("model/logger/data_driven");
class BaseWorkFlow {
    /**
     * 对话流程
     * @param chat_id
     * @param user_id
     * @param userMessage
     */
    static async step(chat_id, user_id, userMessage) {
        throw new Error('Not implemented');
    }
    static async humanInvolveGroupNotify(contactName, courseNo, userMessage) {
        throw new Error('Not implemented');
    }
}
exports.BaseWorkFlow = BaseWorkFlow;
function trackInvoke(target, propertyKey, descriptor) {
    const originalMethod = descriptor.value;
    descriptor.value = async function (...args) {
        const state = args[0]; // 假设 state 对象存在于第一个参数
        const chatId = state.chat_id; // 假设 state 中包含 chatId
        const roundId = state.round_id; // 假设 state 中包含 roundId
        const chatDBClient = new chat_1.ChatDB(prisma_1.PrismaMongoClient.getInstance());
        const chatStateStoreClient = new chat_state_store_1.ChatStateStore(chatDBClient);
        const eventTrackClient = new data_driven_1.EventTracker(prisma_1.PrismaMongoClient.getInstance());
        const nodeInvokeCount = (await chatStateStoreClient.get(state.chat_id)).nodeInvokeCount;
        const node_name = target.name;
        if (!nodeInvokeCount[node_name]) {
            nodeInvokeCount[node_name] = 0;
        }
        const stateInfo = {
            chatId,
            roundId,
            userMessage: state.userMessage
        };
        // 输出进入节点的信息
        logger_1.default.debug({ chat_id: chatId, round_id: roundId }, `进入 ${node_name}`, `调用次数: ${nodeInvokeCount[node_name]}`, stateInfo);
        eventTrackClient.track(chatId, data_driven_1.IEventType.NodeInvoke, {
            chat_id: chatId,
            round_id: roundId,
            node_name: node_name,
            node_count: nodeInvokeCount[node_name]
        });
        const start = Date.now();
        let res;
        try {
            res = await originalMethod.apply(this, args);
        }
        catch (e) {
            logger_1.default.error({ chat_id: chatId, round_id: roundId }, `调用 ${node_name} 出错`, e, `round_id: ${roundId}`); // 输出)
            throw e;
        }
        const end = Date.now();
        logger_1.default.debug({ chat_id: chatId, round_id: roundId }, `结束 ${target.name}`, `执行时长: ${((end - start) / 1000).toFixed(1)}s`, stateInfo); // 输出结束节点的信息
        nodeInvokeCount[node_name]++; // 消息有可能被打断，计数放到后面
        return res;
    };
    return descriptor;
}
//# sourceMappingURL=workflow.js.map