"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RAGHelper = void 0;
const llm_model_1 = require("lib/ai/llm/llm_model");
const xml_1 = require("lib/xml/xml");
const logger_1 = __importDefault(require("model/logger/logger"));
const siliconflow_1 = __importDefault(require("lib/sliconflow/siliconflow"));
const elastic_search_1 = __importStar(require("model/elastic_search/elastic_search"));
const openai_embedding_1 = require("lib/ai/llm/openai_embedding");
const elasticsearch_1 = require("@langchain/community/vectorstores/elasticsearch");
const config_1 = require("config");
const query_rewrite_1 = require("./prompt/query_rewrite");
/**
 * RAG Service，抽象出一些通用方法
 */
class RAGHelper {
    chatHistoryServiceClient;
    constructor(chatHistoryServiceClient) {
        this.chatHistoryServiceClient = chatHistoryServiceClient;
    }
    /**
     * 创建 RAG 库
     * @param index_name 索引名
     * @param metadata 要包含的字段，会使用 keyword 类型
     * @param queryName 查询的 query 字段名, 会自动启用分词，用于后续的混合检索
     */
    static async createRAG(index_name, metadata, queryName) {
        // 构建基础的索引配置
        const indexConfig = {
            properties: {
                embeddings: { type: 'dense_vector', index: true }, // 嵌入向量字段
            },
        };
        // 将 metadata 中的字段添加到索引配置，使用 keyword 类型
        metadata.forEach((field) => {
            indexConfig.properties[`metadata.${field}`] = { type: 'keyword' };
        });
        // 为 queryName 字段创建分词器
        indexConfig.properties[queryName] = {
            type: 'text',
            analyzer: 'ik_max_word',
            search_analyzer: 'ik_smart',
        };
        // 创建索引
        await elastic_search_1.default.createIndex(index_name, indexConfig);
    }
    /**
     * 插入数据，使用 LangChain 来辅助插入
     */
    static async addDocuments(index_name, docs) {
        const clientArgs = {
            client: elastic_search_1.ElasticSearchClient.getInstance(),
            indexName: index_name,
        };
        const embeddings = openai_embedding_1.AzureOpenAIEmbedding.getInstance();
        const vectorStore = new elasticsearch_1.ElasticVectorSearch(embeddings, clientArgs);
        const batchSize = 100;
        const batches = Math.ceil(docs.length / batchSize);
        for (let i = 0; i < batches; i++) {
            const batch = docs.slice(i * batchSize, (i + 1) * batchSize);
            await vectorStore.addDocuments(batch);
            logger_1.default.trace(`Insert ${batch.length} documents`);
        }
    }
    static async addDocumentsWithIds(index_name, docs, ids) {
        const clientArgs = {
            client: elastic_search_1.ElasticSearchClient.getInstance(),
            indexName: index_name,
        };
        const embeddings = openai_embedding_1.AzureOpenAIEmbedding.getInstance();
        const vectorStore = new elasticsearch_1.ElasticVectorSearch(embeddings, clientArgs);
        const batchSize = 100;
        const batches = Math.ceil(docs.length / batchSize);
        for (let i = 0; i < batches; i++) {
            const batch = docs.slice(i * batchSize, (i + 1) * batchSize);
            const batchIds = ids.slice(i * batchSize, (i + 1) * batchSize);
            await vectorStore.addDocuments(batch, { ids: batchIds });
            logger_1.default.trace(`Insert ${batch.length} documents`);
        }
    }
    /**
     *
     * 将 Embedding 检索后的内容进行重排序
     * @param originQuery 重写的客户问题(string)
     * @param retrievedQAs embedding search得到的k个问题
     * @param options
     * @private
     */
    static async reRank(originQuery, retrievedQAs, options) {
        if (!retrievedQAs.length) {
            return [];
        }
        const siliconFlow = new siliconflow_1.default();
        siliconFlow.auth(config_1.Config.setting.siliconFlow.apiKey);
        try {
            const response = await siliconFlow.createRerank({
                model: options?.model ? options?.model : 'BAAI/bge-reranker-v2-m3',
                query: originQuery,
                documents: retrievedQAs.map((qa) => qa.q),
                top_n: options?.top_n,
                ...options,
            });
            // 根据相关性得分对结果进行排序
            const sortedResults = response.results.sort((a, b) => b.relevance_score - a.relevance_score);
            // 返回排序后的文档
            return sortedResults.map((sortedResult) => {
                return {
                    q: retrievedQAs[sortedResult.index].q,
                    a: retrievedQAs[sortedResult.index].a,
                    score: sortedResult.relevance_score,
                };
            });
        }
        catch (error) {
            console.error('Error in reRanker:', error);
            return retrievedQAs.map((qa) => {
                return {
                    q: qa.q,
                    a: qa.a,
                    score: 0,
                };
            });
        }
    }
    static removeDuplicateQA(reRankResults) {
        const set = new Set();
        return reRankResults.filter((qa) => {
            const combined = qa.q + qa.a;
            if (!set.has(combined)) {
                set.add(combined);
                return true;
            }
            return false;
        });
    }
    static isNeedRAG(query) {
        if (!query || (query.startsWith('[') && query.endsWith(']'))) {
            // 输入过滤
            return false;
        }
        // 检查是否需要跳过RAG查询
        const shouldSkipRag = /\d{11,}/.test(query) ||
            /^[a-zA-Z]+$/.test(query) ||
            query.trim().length <= 2;
        if (shouldSkipRag) {
            return false;
        }
        return true;
    }
    async mergeQuery(inputQuery, chat_id) {
        const chatHistory = await this.chatHistoryServiceClient.getChatHistoryByChatId(chat_id);
        for (let i = chatHistory.length - 2; i >= 0; i--) {
            if (chatHistory[i].role === 'user') {
                inputQuery = `${chatHistory[i].content}\n${inputQuery}`;
            }
            else {
                return inputQuery;
            }
        }
        return inputQuery;
    }
    /**
     * 返回 改写的 subQuery + 原始 query
     * @param query
     * @param topic
     * @param chat_id
     * @param round_id
     */
    async queryReWriteWithChatHistory(query, topic, chat_id, round_id) {
        try {
            const chatHistory = await this.chatHistoryServiceClient.getChatHistory(chat_id, 3, 6);
            const prompt = await query_rewrite_1.QueryRewritePrompt.format(query, topic, chatHistory);
            const llm = new llm_model_1.LLM({
                model: 'gpt-5-mini',
                meta: {
                    promptName: query_rewrite_1.QueryRewritePrompt.name,
                    chat_id: chat_id,
                    round_id: round_id,
                },
            }); // 添加日志，方便追踪
            const extractedRawQuery = await llm.predict(prompt);
            const subQueries = xml_1.XMLHelper.extractContents(extractedRawQuery, 'query');
            if (!subQueries) {
                return [query];
            }
            return [query, ...subQueries];
        }
        catch (e) {
            logger_1.default.error('Error in query rewrite:', e);
            return [query];
        }
    }
}
exports.RAGHelper = RAGHelper;
//# sourceMappingURL=rag.js.map