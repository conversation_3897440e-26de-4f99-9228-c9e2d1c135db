import logger from 'model/logger/logger'

// 通用阶段清单（唯一来源）
export const STAGES = [
  'afterAdding',  // 挖需
  'afterBonding',  // 挖需后
  'afterCourse1',
  'afterCourse2',
  'afterCourse3',
  'afterCourse4',
  'afterCourse5',
  'afterCourse6',
  'afterPaid',
  'coursePostpone',  // 延期
  'duringCourse',  // 课中
] as const
type Stage = typeof STAGES[number]

// 辅助类型：将阶段名映射到某种值类型
export type StageMap<T> = Record<Stage, T>

// —— 中央注册表：按阶段获取元行为与思考提示词（项目级只需注册一次，不再在 STAGES 里逐项填写）
let META_ACTIONS: Partial<StageMap<Record<string, string>>> = {}
let THINK_PROMPT: Partial<StageMap<string>> = {}

export function registerMetaActions(map: Partial<StageMap<Record<string, string>>>): void { META_ACTIONS = map }
export function registerThinkPrompt(map: Partial<StageMap<string>>): void { THINK_PROMPT = map }

export type ActionMap = Record<string, (chat_id: string, round_id: string, strategy: string, actions: string[]) => Promise<ActionInfo>>

export interface ActionInfo {
  guidance: string
  callback?: () => Promise<void>
}

export interface StageSpec {
  /** 阶段标识，仅用于调试/日志 */
  id: Stage
  /** 阶段是否激活 */
  isActive: (chatId: string) => Promise<boolean>
  /** 阶段可用动作：可以是静态映射、也可以是按需异步生成 */
  actions?: ActionMap | null | (() => Promise<ActionMap | null>)
}

export class GenericStage {
  public readonly spec: StageSpec
  constructor(spec: StageSpec) {
    this.spec = spec
  }

  public async activeStatus(chatId: string): Promise<boolean> {
    return this.spec.isActive(chatId)
  }

  public async getAction(): Promise<ActionMap | null> {
    const { actions } = this.spec
    if (!actions) return null
    if (typeof actions === 'function') return await actions()
    return actions
  }

  public async getMetaActions(): Promise<Record<string, string>> {
    const metaActions = META_ACTIONS[this.spec.id]
    if (!metaActions) {
      throw new Error(`未配置阶段 ${this.spec.id} 的 MetaActions，请通过 registerMetaActions 注册`)
    }
    return metaActions
  }

  public async getThinkPrompt(): Promise<string> {
    const thinkPrompt = THINK_PROMPT[this.spec.id]
    if (!thinkPrompt) {
      throw new Error(`未配置阶段 ${this.spec.id} 的 ThinkPrompt，请通过 registerThinkPrompt 注册`)
    }
    return thinkPrompt
  }
}

export class StageFilter {
  private readonly stages: GenericStage[]
  private actionMap: ActionMap = {}

  constructor(stageList: GenericStage[]) {
    this.stages = stageList
  }

  private async registerActionList() {
    for (const stage of this.stages) {
      const actionList = await stage.getAction()
      if (actionList) {
        this.actionMap = { ...this.actionMap, ...actionList }
      }
    }
  }

  public async handleAction(chat_id: string, round_id: string, strategy: string | string[], actions: string[]): Promise<ActionInfo> {
    if (Object.keys(this.actionMap).length === 0) {
      await this.registerActionList()
    }
    const actionInfo: ActionInfo = { guidance: '' }
    if (!this.actionMap || Object.keys(this.actionMap).length == 0) {
      return actionInfo
    }
    for (const action of actions) {
      if (this.actionMap[action]) {

        const strategyStr: string = Array.isArray(strategy)
          ? strategy[0] ?? ''
          : strategy
        return await this.actionMap[action](chat_id, round_id, strategyStr,  actions)
      }
    }
    return actionInfo
  }

  public async getStageInfo(chat_id: string) {
    if (Object.keys(this.actionMap).length === 0) {
      await this.registerActionList()
    }
    let activeStage: GenericStage | null = null
    for (const stage of this.stages) {
      if (await stage.activeStatus(chat_id)) {
        activeStage = stage
        break
      }
    }
    if (!activeStage) {
      logger.error({ chat_id: chat_id }, '没有可用的元行为组')
      return {
        name: '',
        thinkPrompt: '',
        metaActions: '',
      }
    }
    logger.trace({ chat_id: chat_id }, `当前使用的元行为组：${activeStage.spec.id}`)
    return {
      name: activeStage.spec.id,
      thinkPrompt: await activeStage.getThinkPrompt(),
      metaActions: this.formatMetaAction(await activeStage.getMetaActions()),
    }
  }

  private formatMetaAction(metaAction: Record<string, string>): string {
    return Object.entries(metaAction).map(([key, value]) => `- ${key}：${value}`).join('\n')
  }
}