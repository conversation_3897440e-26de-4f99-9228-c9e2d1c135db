"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MetaActionRouter = void 0;
const logger_1 = __importDefault(require("model/logger/logger"));
class MetaActionRouter {
    components;
    actionMap = {};
    constructor(componentList) {
        this.components = componentList;
    }
    async getStageName(chat_id) {
        let activeComponent = null;
        for (const component of this.components) {
            if (await component.activeStatus(chat_id)) {
                activeComponent = component;
                break;
            }
        }
        return activeComponent?.constructor.name || '';
    }
    async registerActionList() {
        for (const component of this.components) {
            const actionList = await component.getAction();
            if (actionList) {
                this.actionMap = { ...this.actionMap, ...actionList };
            }
        }
    }
    async handleAction(chat_id, round_id, actions) {
        if (Object.keys(this.actionMap).length === 0) {
            await this.registerActionList();
        }
        const actionInfo = { guidance: '' };
        if (!this.actionMap || Object.keys(this.actionMap).length == 0) {
            return actionInfo;
        }
        for (const action of actions) {
            if (this.actionMap[action]) {
                return await this.actionMap[action](chat_id, round_id);
            }
        }
        return actionInfo;
    }
    async getThinkAndMetaActions(chat_id) {
        if (Object.keys(this.actionMap).length === 0) {
            await this.registerActionList();
        }
        let activeComponent = null;
        for (const component of this.components) {
            if (await component.activeStatus(chat_id)) {
                activeComponent = component;
                break;
            }
        }
        if (!activeComponent) {
            logger_1.default.error({ chat_id: chat_id }, '没有可用的元行为组');
            return {
                thinkPrompt: '',
                metaActions: '',
                guidance: '',
            };
        }
        logger_1.default.trace({ chat_id: chat_id }, `当前使用的元行为组：${activeComponent.constructor.name}`);
        return {
            thinkPrompt: await activeComponent.getThinkPrompt(),
            metaActions: this.formatMetaAction(await activeComponent.getMetaAction()),
            guidance: await activeComponent.getGuidance(chat_id),
        };
    }
    formatMetaAction(metaAction) {
        return Object.entries(metaAction).map(([key, value]) => `- ${key}：${value}`).join('\n');
    }
}
exports.MetaActionRouter = MetaActionRouter;
//# sourceMappingURL=router.js.map