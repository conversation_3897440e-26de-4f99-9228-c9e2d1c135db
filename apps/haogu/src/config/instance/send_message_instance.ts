import { chatDBClient, chatHistoryServiceClient } from './base_instance'
import { FreeThink } from 'service/agent/freethink'
import { Reply } from 'service/agent/reply'
import { EventTracker } from 'model/logger/data_driven'
import { PrismaMongoClient } from '../../database/prisma'
import { ChatIdTransfer } from '../chat_id_transfer'
import { PrismaMongoClient as PrismaMongoCommonClient } from 'model/mongodb/prisma'
import { MaterialManager } from '../../workflow/helper/material_manager'
import { WecomMessageSender } from 'service/message_handler/juzi/message_sender'
import { WecomCommonMessageSender } from 'service/visualized_sop/common_sender/wecom'

export const chatIdTransfer = new ChatIdTransfer(PrismaMongoClient.getInstance(), PrismaMongoCommonClient.getConfigInstance())
export const materialManager = new MaterialManager()
// export const haoguMessageSender = new HaoguMessageSender(haoguScrmApi, chatHistoryServiceClient)
// export const commonMessageSender = new HaoguCommonMessageSender(haoguMessageSender, chatHistoryServiceClient, chatIdTransfer, materialManager)
export const wecomMessageSender = new WecomMessageSender(chatHistoryServiceClient)
export const commonMessageSender = new WecomCommonMessageSender(wecomMessageSender, chatHistoryServiceClient)

export const replyClient = new Reply(chatDBClient, chatHistoryServiceClient, commonMessageSender)

