import {
  ActionInfo,
  GenericStage,
  registerMetaActions,
  registerThinkPrompt,
  StageFilter,
  StageSpec
} from 'service/agent/stage'
import { chatStateStoreClient } from '../../config/instance/base_instance'
import { DataService } from '../helper/get_data'
import { MetaActions, ThinkPrompt } from './context'
import { PostAction } from './post_action'
import { SendFileAction } from './send_file_action'

// Keep the special action previously defined inside AfterCourse1
async function sendCourseReplay(chat_id: string): Promise<ActionInfo> {
  const course1Backup = (await DataService.getCourseLinkByChatId(chat_id, 1)) ?? ''
  return { guidance: `给客户发送课程回放${course1Backup}` }
}

// ---- Stage registry (declarative) ----
registerMetaActions(MetaActions)
registerThinkPrompt(ThinkPrompt)
const STAGES: StageSpec[] = [
  {
    id: 'duringCourse',
    isActive: async (chatId) => {
      return await DataService.isInCourseTimeLine(chatId, 'inCourse') && !await DataService.isInCourseTimeLine(chatId, 'afterSales')
    },
  },
  {
    id: 'afterCourse6',
    isActive: async (chatId) => await DataService.isInCourseTimeLine(chatId, 'afterCourse', 6),
    actions: {
      // '强推陪跑营服务': PostAction.sendCourseIntro,
      // '发送成功案例': PostAction.sendCaseImage,
      '发起购买邀约': PostAction.sendInvitation,
      '保留名额': PostAction.reaskAnotherDay,
      '发送文件': SendFileAction.send
    },
  },
  {
    id: 'afterCourse4',
    isActive: async (chatId) => await DataService.isInCourseTimeLine(chatId, 'afterSales', 4),
    actions: {
      // '发送成功案例': PostAction.sendCaseImage,
      '发起购买邀约': PostAction.sendInvitation,
      '保留名额': PostAction.reaskAnotherDay,
      '发送文件': SendFileAction.send
    },
  },
  {
    id: 'afterCourse1',
    isActive: async (chatId) => await DataService.isInCourseTimeLine(chatId, 'afterSales', 1),
    actions: async () => ({
      // '发送成功案例': PostAction.sendCaseImage,
      // '提供延期方案': PostAction.enterPostpone,
      '发送回放': sendCourseReplay,
      '发送文件': SendFileAction.send
    }),
  },
  {
    id: 'afterBonding',
    isActive: async (chatId) => {
      const chatState = await chatStateStoreClient.get(chatId)
      return await DataService.isInCourseTimeLine(chatId, 'beforeCourse', 1) && (chatState.state.after_bonding as boolean)
    },
    actions: async () => ({
      // '发送成功案例': PostAction.sendCaseImage,
      // '提供延期方案': PostAction.enterPostpone,
      '发送文件': SendFileAction.send
    }),
  },
  {
    id: 'afterAdding',
    isActive: async (_chatId) => true,
    actions: {
      '发送文件': SendFileAction.send,
      '发送预习视频': PostAction.sendPreviewVideo,
      '发送安装教程': PostAction.sendInstallationGuide,
      // '发送第一组题': PostAction.askQuestionOne,
      // '发送第二组题': PostAction.askQuestionTwo,
      // '发送第三组题': PostAction.askQuestionThree,
      // '生成评测结果': PostAction.askResultLevel,
      '结束挖需': PostAction.sendEconomicCurve,
    },
  },
]

// ---- Router wiring (order matters: strictly reverse chronological flow) ----
export const stageFilter = new StageFilter(
  STAGES.map((spec) => new GenericStage(spec))
) // 必须严格按照流程倒序添加