import { ContentItem, ContentItemMediaType, ContentItemType } from 'model/haogu/crm/type'
import { haoguScrmApi } from '../../config/instance/api_instance'
import { PrismaMongoClient } from '../../database/prisma'
import { RAGHelper } from 'service/rag/rag_helper'
import ElasticSearchService from 'model/elastic_search/elastic_search'
import { DataService } from './get_data'

export enum MaterialType {
  Text = 1,
  Article = 2,
  File = 3,
  Link = 4,
  Poster = 5,
  Image = 6,
  Audio = 7,
  Video = 8,
  Voice = 9,
  Channels = 10,
}

export interface IMaterial {
  source_id:string
  type:MaterialType
  title:string
  description:string
  es_id:string
  enable:boolean
  main_category:string
  sub_category:string
  data:object
}

const RAG_INDEX = 'haogu_material'


export class MaterialManager {
  public async searchMaterialFromMogo(sourceId: string) {
    return PrismaMongoClient.getInstance().material.findFirst({
      where: {
        source_id: sourceId
      }
    })
  }

  public async searchMaterialByTitle(title: string, category: string) {
    const index = 'haogu_material'
    const filter =  {
      bool:{
        must:[
          {
            match:{
              'metadata.category':category
            }
          }
        ]
      }
    }


    const searchRes =  await ElasticSearchService.embeddingSearch(
      index,
      title,
      1,
      0.74,
      filter
    )

    return new MaterialManager().searchMaterialFromMogo(searchRes[0].metadata.source_id)

  }

  public async saveMaterial(mainCategory:string, subCategory:string, item: ContentItem) {
    const title = item.title || ''
    const description = item.desc || item.sphfeed_desc || item.content || title || ''
    await PrismaMongoClient.getInstance().material.create({
      data: {
        source_id:item.id.toString(),
        type:this.ItemTypeToMediaType(item),
        title:title,
        description:description,
        main_category:mainCategory,
        sub_category:subCategory,
        enable:false,
        es_id:'',
        data:JSON.parse(JSON.stringify(item))
      }
    })
  }

  public async deleteMaterial(id: string) {
    // 先获取素材信息（包括es_id）
    const material = await PrismaMongoClient.getInstance().material.findUnique({
      where: { id }
    })

    if (!material) {
      throw new Error('素材不存在')
    }

    // 删除ES中的数据
    if (material.es_id) {
      try {
        await ElasticSearchService.deleteDocuments(RAG_INDEX, [material.es_id])
      } catch (error) {
        console.error('删除ES文档失败:', error)
        // 继续删除MongoDB数据，即使ES删除失败
      }
    }

    // 删除MongoDB中的数据
    await PrismaMongoClient.getInstance().material.delete({
      where: { id }
    })
  }

  public async enableMaterial(id: string) {
    // 获取素材信息
    const material = await PrismaMongoClient.getInstance().material.findUnique({
      where: { id }
    })

    if (!material) {
      throw new Error('素材不存在')
    }

    // 如果已经有es_id，说明已经在ES中，直接返回
    if (material.es_id && material.enable) {
      return
    }

    // 添加到ES
    const [esId] = await RAGHelper.addDocuments(RAG_INDEX, [{
      pageContent: material.title,
      metadata: {
        title: material.title,
        description: material.description,
        source_id: material.source_id,
        category: material.main_category
      }
    }])

    // 更新MongoDB中的es_id和enable状态
    await PrismaMongoClient.getInstance().material.update({
      where: { id },
      data: {
        es_id: esId,
        enable: true
      }
    })
  }

  public async disableMaterial(id: string) {
    // 获取素材信息
    const material = await PrismaMongoClient.getInstance().material.findUnique({
      where: { id }
    })

    if (!material) {
      throw new Error('素材不存在')
    }

    // 从ES中删除
    if (material.es_id) {
      try {
        await ElasticSearchService.deleteDocuments(RAG_INDEX, [material.es_id])
      } catch (error) {
        console.error('从ES删除文档失败:', error)
        // 继续更新MongoDB，即使ES删除失败
      }
    }

    // 更新MongoDB，清空es_id并设置enable为false
    await PrismaMongoClient.getInstance().material.update({
      where: { id },
      data: {
        es_id: '',
        enable: false
      }
    })
  }

  public async updateMaterialContent(id: string, updates: { title?: string; description?: string }) {
    // 获取素材信息
    const material = await PrismaMongoClient.getInstance().material.findUnique({
      where: { id }
    })

    if (!material) {
      throw new Error('素材不存在')
    }

    // 更新MongoDB
    const updatedMaterial = await PrismaMongoClient.getInstance().material.update({
      where: { id },
      data: updates
    })

    // 如果素材已启用且有es_id，同时更新ES中的数据
    if (material.enable && material.es_id) {
      try {
        // 先删除旧的ES文档
        await ElasticSearchService.deleteDocuments(RAG_INDEX, [material.es_id])

        // 重新添加更新后的文档
        const [newEsId] = await RAGHelper.addDocuments(RAG_INDEX, [{
          pageContent: updates.title || material.title,
          metadata: {
            title: updates.title || material.title,
            description: updates.description || material.description,
            source_id: material.source_id,
            category: material.main_category
          }
        }])

        // 更新MongoDB中的es_id
        await PrismaMongoClient.getInstance().material.update({
          where: { id },
          data: { es_id: newEsId }
        })
      } catch (error) {
        console.error('更新ES文档失败:', error)
        // ES更新失败不影响MongoDB的更新
      }
    }

    return updatedMaterial
  }

  public async searchMaterialFromHaoGu(page:number, pageSize:number, type: number, mediaType?: number) {
    const res = await haoguScrmApi.contentList(page, pageSize, 2540, type, mediaType, 1)

    return res
  }


  public async isValidCourseMaterial(chatId: string, title: string) {
    const currentTime = await DataService.getCurrentTime(chatId)

    const coursePattern = /第([一二三四五六])节课(预习视频|课后作业|课程笔记)/
    const match = title.match(coursePattern)

    if (!match) {
      return false
    }

    const chineseNumbers: { [key: string]: number } = {
      '一': 1, '二': 2, '三': 3, '四': 4, '五': 5, '六': 6
    }

    const lessonNumber = chineseNumbers[match[1]]
    if (!lessonNumber) {
      return false
    }


    return currentTime.day > lessonNumber

  }

  private ItemTypeToMediaType(item: ContentItem) {
    switch (item.type) {
      case ContentItemType.Article:
        return MaterialType.Article
      case ContentItemType.File:
        return MaterialType.File
      case ContentItemType.Link:
        return MaterialType.Link
      case ContentItemType.Poster:
        return MaterialType.Poster
      case ContentItemType.Channels:
        return MaterialType.Channels
      case ContentItemType.Text:
        return MaterialType.Text
      case ContentItemType.Media:
        switch (item.media_type) {
          case ContentItemMediaType.Image:
            return MaterialType.Image
          case ContentItemMediaType.Audio:
            return MaterialType.Audio
          case ContentItemMediaType.Video:
            return MaterialType.Video
          case ContentItemMediaType.Voice:
            return MaterialType.Voice
        }
    }

    return MaterialType.Text
  }
}