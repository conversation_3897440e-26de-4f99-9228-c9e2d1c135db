import { DateHelper } from 'lib/date/date'
import dayjs from 'dayjs'
import { haoguLiveAPI } from '../../config/instance/api_instance'
import { chatDBClient, chatStateStoreClient } from '../../config/instance/base_instance'
import logger from 'model/logger/logger'
import { IChattingFlag } from '../../config/manifest'
import { eventTrackClient } from '../../config/instance/event_track_instance'
import { IEventType } from 'model/logger/data_driven'
import { RedisDB } from 'model/redis/redis'

export interface IScheduleTime {
  is_course_day?: boolean // true 表示上课日，false 表示上课前
  post_course_day?: number // 课程结束后的第几天，1 表示第一天，2 表示第二天，依此类推
  day: number // 1-7 表示上课日第几天，负数代表上课前的几天，例如 0 表示上课前1天，-1 表示上课前2天，依此类推
  time: string // 格式为 'HH:MM:SS'，例如 '08:00:00'
}

export interface ICourseInfo {
  liveId: string
  liveName: string
  day: number
  liveLink: string
}

export class DataService {
  /**
     * 判断当前时间是否在指定课程时间范围内
     * @param chatId 聊天ID，用于获取当前时间
     * @param timeline 时间线类型，可选值：'beforeCourse' | 'afterCourse' | 'afterSales' | 'inCourse'，默认为 'inCourse'
     * @param day 天数，可选参数，默认为当前天
     * @returns 返回布尔值，表示是否在指定时间范围内
     */
  public static async isInCourseTimeLine(chatId: string, timeline: 'beforeCourse' | 'afterCourse' | 'afterSales' | 'inCourse' = 'inCourse', day?: number) {
    const currentTime = await DataService.getCurrentTime(chatId)
    if (!day) {
      day = currentTime.day
    }

    // 课程时间配置
    const COURSE_TIMES: Record<number, Record<string, string>> = {
      1: { before: '19:20:00', after: '20:44:00', sales: '20:44:00' },
      2: { before: '19:20:00', after: '20:50:00', sales: '20:50:00' },
      3: { before: '19:20:00', after: '20:53:00', sales: '20:53:00' },
      4: { before: '19:20:00', after: '20:55:00', sales: '20:55:00' },
      5: { before: '19:20:00', after: '20:54:00', sales: '20:54:00' },
      6: { before: '19:20:00', after: '20:52:00', sales: '20:52:00' },
    }

    // 处理跨天情况的核心逻辑

    // 非当天或课程时间内inCourse返回false
    if (currentTime.day != day && timeline === 'inCourse' && day < 1 && day > 5) {
      return false
    }
    // 天数更小beforeCourse为true
    if (currentTime.day < day) {
      return timeline === 'beforeCourse'
    }
    // 天数更小afterSales为false
    if (currentTime.day < day && timeline === 'afterSales') {
      return false
    }
    // 天数更大afterCourse和afterSales为true
    if (currentTime.day > day) {
      return timeline === 'afterCourse' || timeline === 'afterSales'
    }
    // 开课前beforeCourse为true
    if (currentTime.day <= 0) {
      return timeline === 'beforeCourse'
    }
    // 开课前afterSales为false
    if (timeline === 'afterSales' && day < 1) {
      return false
    }
    // 课程结束afterCourse和afterSales为true
    if (currentTime.day >= 5) {
      return timeline === 'afterCourse' || timeline === 'afterSales'
    }

    // 处理当天情况的核心逻辑
    const { before: beforeTime, after: afterTime, sales: salesTime } = COURSE_TIMES[day]
    if (timeline === 'inCourse') {
      return DateHelper.isTimeBefore(currentTime.time, afterTime) && DateHelper.isTimeAfter(currentTime.time, beforeTime)
    }
    if (timeline === 'beforeCourse') {
      return DateHelper.isTimeBefore(currentTime.time, beforeTime)
    } else if (timeline === 'afterCourse') {
      return !DateHelper.isTimeBefore(currentTime.time, afterTime)
    } else if (timeline === 'afterSales') {
      return salesTime ? !DateHelper.isTimeBefore(currentTime.time, salesTime) : false
    }

    return false
  }

  public static async getCourseLinkByChatId(chatId: string, courseDay: number) {
    const chat = await chatDBClient.getById(chatId)
    if (!chat) {
      return ''
    }

    if (!chat.course_no) {
      return ''
    }

    return this.getCourseLinkByCourseNo(chat.course_no, courseDay)
  }

  public static getCurrentCourseNo(): number {
    // 直接按格式输出并转成数字
    const now = dayjs()
    return Number(now.format('YYYYMMDD'))
  }

  public static async getCourseStartTimeByChatId(chatId: string) {
    let courseNo = await chatDBClient.getCourseNo(chatId)
    if (!courseNo) {
      //找不到则认为刚加入
      courseNo = this.getCurrentCourseNo()
    }
    return this.getCourseStartTime(courseNo)
  }

  private static getCourseStartTime(courseNo: number): Date {
    return dayjs(courseNo.toString(10))
      .add(1, 'day')
      .hour(19)
      .minute(20)
      .second(0)
      .toDate()
  }

  public static async getCurrentTime(chatId: string): Promise<IScheduleTime> {
    // TODO:for test
    const startDate = await this.getCourseStartTimeByChatId(chatId)
    const redisClient = RedisDB.getInstance()
    const offset = (await redisClient.get(`haogu:offset:${chatId}`)) ?? 0

    const currentDate = dayjs().add(Number(offset), 'hour').toDate()

    // 计算当前时间与开课时间的天数差
    const timeDiffInDays = dayjs(currentDate)
      .startOf('day')
      .diff(dayjs(startDate).startOf('day'), 'day')

    const dayDiff = timeDiffInDays + 1

    const scheduleTime: IScheduleTime = {
      day: dayDiff, //后续会更新
      time: DateHelper.formatDate(currentDate, 'HH:mm:ss'),
    }

    if (dayDiff < 1) {
      scheduleTime.day = dayDiff
      scheduleTime.is_course_day = false
    } else if (dayDiff >= 1 && dayDiff <= 6) {
      scheduleTime.day = dayDiff
      scheduleTime.is_course_day = true
    } else {
      scheduleTime.day = dayDiff
      scheduleTime.is_course_day = false
      scheduleTime.post_course_day = dayDiff - 6
    }

    return scheduleTime
  }

  /**
   * 主动获取课程完成情况，课程可以传 1，2，3，4，5，6
   */
  public static async isCompletedCourse(
    chatId: string,
    day: number,
  ): Promise<boolean> {
    const min = 30
    const state = await chatStateStoreClient.getFlags<IChattingFlag>(chatId)
    if (day >= 1 && day <= 6) {
      if (state[`is_complete_course_day${day}`]) {
        return true
      } else {
        const result = await this.isAttendCourseMoreThanCertainDuration(chatId, day, min)
        if (result) {
          await chatStateStoreClient.update(chatId, { state:<IChattingFlag>{ ...state, [`is_attend_course_day${day}`]:true, [`is_complete_course_day${day}`]:true } })
          eventTrackClient.track(chatId, IEventType.CourseComplete, { 'day': day })
          return true
        } else {
          return false
        }
      }
    } else {
      throw (`param day is wrong, day is ${day}`)
    }
  }


  public static async isAttendCourse(
    chatId: string,
    day: number,
  ): Promise<boolean> {
    try {
      const min = 1
      const state = await chatStateStoreClient.getFlags<IChattingFlag>(chatId)
      if (day >= 1 && day <= 6) {
        if (state[`is_attend_course_day${day}`]) {
          return true
        } else {
          const result = await this.isAttendCourseMoreThanCertainDuration(chatId, day, min)
          if (result) {
            await chatStateStoreClient.update(chatId, { state:<IChattingFlag>{ ...state, [`is_attend_course_day${day}`]:true } })
            eventTrackClient.track(chatId, IEventType.CourseArrive, { 'day': day })
            return true
          } else {
            return false
          }
        }
      } else {
        logger.error(`param day is wrong, day is ${day}`)
        return false
      }
    } catch (e) {
      return false
    }
  }

  public static async getCourseLinkByCourseNo(courseNo: number, day: number) {
    const courseInfo = await this.getCourseInfoByCourseNo(courseNo)

    if (!courseInfo) {
      return null
    }

    return courseInfo.find((item) => item.day === day)?.liveLink ?? null
  }

  public static async getCourseInfoByCourseNo(
    courseNo: number,
  ): Promise<ICourseInfo[] | null> {
    const courseDate = dayjs(courseNo.toString(10))
      .add(1, 'day')
      .format('YYYYMMDD')

    const data = await haoguLiveAPI.getCourseInfoByCourseName(`AI营销${courseDate}`)

    if (!data) {
      return null
    }

    return data.map((item, index) => ({
      liveId: item.liveId,
      liveName: item.liveName,
      day: index + 1,
      liveLink: item.shortUrl // 注意直播链接和回放链接是同一个
    }))
  }

  public static async getCourseWatchTime(chatId: string, day: number) {
    if (day < 1 || day > 6) {
      return null
    }

    try {
      const chat = await chatDBClient.getById(chatId)
      if (!chat) {
        throw new Error('chat is not found')
      }

      if (!chat.course_no) {
        throw new Error('courseNo is not set')
      }

      const courseInfo = await this.getCourseInfoByCourseNo(chat.course_no)
      if (!courseInfo) {
        throw new Error(`找不到课程信息: ${chat.course_no}`)
      }

      const course = courseInfo.find((course) => course.day === day)

      if (!course) {
        throw new Error(`找不到第 ${day} 节课: ${JSON.stringify(courseInfo)}`)
      }

      const watchTime = await haoguLiveAPI.queryUserWatchList({ data: { channelId: course.liveId, wxUnionId: chat.wx_union_id } })
      if (!watchTime) {
        throw new Error(`找不到观看时间: ${JSON.stringify({ channelId: course.liveId, wxUnionId: chat.wx_union_id })}`)
      }

      if (!watchTime.data || !watchTime.data.length) {
        throw new Error(`找不到观看时间: ${JSON.stringify({ channelId: course.liveId, wxUnionId: chat.wx_union_id })}`)
      }


      return watchTime.data[0]
    } catch (e) {
      logger.error('获取上课时间失败', e)
      return null
    }
  }


  /**
   * 判断是否上课超过一定分钟数
   */
  public static async isAttendCourseMoreThanCertainDuration(
    chatId: string,
    day: number,
    min: number,
  ): Promise<boolean> {
    try {
      const watchTime = await this.getCourseWatchTime(chatId, day)
      if (!watchTime) {
        return false
      }

      return watchTime.totalStudyTime >= min * 60
    } catch (e) {
      logger.error('判断是否上课超过一定分钟数失败', e)

      return false
    }
  }

  public static async getTodayCourse(chatId: string) {
    const currentTime = await DataService.getCurrentTime(chatId)
    let todayCourse = ''
    let tomorrowCourse = ''
    if (currentTime.day > 0 && currentTime.day < 7) {
      const isAfterCourse = await DataService.isInCourseTimeLine(chatId, 'afterCourse', currentTime.day)
      const isInCourse = await DataService.isInCourseTimeLine(chatId, 'inCourse', currentTime.day)
      const courseStatus = isAfterCourse ? '（已结束）' : (isInCourse ? '（已开始）' : '')
      todayCourse  = `今日课程：第${currentTime.day}课${courseStatus}。`
    } else if (currentTime.day >= 7) {
      todayCourse = '好股直播课程已结束'
    }
    if (currentTime.day >= 0 && currentTime.day <= 5) {
      tomorrowCourse = `明日课程：第${currentTime.day + 1}课`
    }
    return `${todayCourse}${tomorrowCourse}`
  }

  public static async isPaidSystemCourse(chatId: string):Promise<boolean> {
    const flags = await chatStateStoreClient.getFlags<IChattingFlag>(chatId)
    return Boolean(flags.is_complete_payment)
  }


  public static async isBookLiveStream(chatId: string, day: number) {

    const chat = await chatDBClient.getById(chatId)

    let courseNo = chat?.course_no

    if (!courseNo) {
      courseNo = this.getCurrentCourseNo()
    }

    const courseInfo = await this.getCourseInfoByCourseNo(courseNo)
    const currentCourse = courseInfo?.find((course) => course.day === day)

    if (!currentCourse) return false

    const unionId = chat?.wx_union_id
    const liveId = currentCourse.liveId

    const bookData = await haoguLiveAPI.getBookByLiveId({ liveId, unionId })

    if (!bookData || bookData.length === 0) {
      return false
    }
    return true
  }
}